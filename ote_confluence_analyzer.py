# ote_confluence_analyzer.py
"""
OTE Confluence Analyzer - OTE + Order Block Kesişimi Analizi
===========================================================

Bu modül, Optimal Trade Entry (OTE) seviyeleri ile Order Block'ların
kesişimini analiz ederek yüksek kaliteli confluence sinyalleri üretir.

ICT OTE Konsepti:
- OTE Zone: %61.8-79% Fibonacci retracement bölgesi
- En iyi giriş noktaları genellikle bu bölgede bulunur
- Order Block ile kesişim confluence kalitesini artırır

Single Responsibility İlkesi:
- Sadece OTE + OB confluence analizinden sorumlu
- fibonacci_analyzer ve order_block_analyzer çıktılarını kullanır
- Temiz, test edilebilir ve yeniden kullanılabilir kod
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from loguru import logger
from datetime import datetime


class OTEConfluenceAnalyzer:
    def _find_ote_poi_confluence(self, ote_zone, order_blocks, fvgs, symbol, tolerance_pips=5):
        """
        OTE bölgesi ile Order Block ve FVG'lerin kesişimini analiz eder.
        Args:
            ote_zone: OTE bölgesi dict
            order_blocks: Order Block listesi
            fvgs: FVG listesi
            symbol: Sembol adı
            tolerance_pips: Yakınlık toleransı
        Returns:
            confluence_points: Kesişim noktaları listesi
        """
        confluence_points = []
        try:
            ote_top = ote_zone.get('top')
            ote_bottom = ote_zone.get('bottom')
            ote_mid = ote_zone.get('mid')
            direction = ote_zone.get('direction')
            # Order Block kesişimleri
            for ob in order_blocks:
                ob_high = ob.get('high')
                ob_low = ob.get('low')
                ob_type = ob.get('type', '').lower()
                # OTE ile OB arasında overlap var mı?
                overlap_top = min(ote_top, ob_high)
                overlap_bottom = max(ote_bottom, ob_low)
                if overlap_top > overlap_bottom:
                    overlap_mid = (overlap_top + overlap_bottom) / 2
                    score = 70
                    # Direction uyumu
                    if (direction == 'bullish' and 'bull' in ob_type) or (direction == 'bearish' and 'bear' in ob_type):
                        score += 10
                    # Fiyat yakınlığı
                    score += max(0, 10 - abs(overlap_mid - ote_mid) / tolerance_pips)
                    confluence_points.append({
                        'intersection_mid': overlap_mid,
                        'intersection_top': overlap_top,
                        'intersection_bottom': overlap_bottom,
                        'score': score,
                        'type': 'order_block',
                        'direction': direction,
                        'order_block': ob,
                        'ote_zone': ote_zone
                    })
            # FVG kesişimleri
            for fvg in fvgs:
                fvg_top = fvg.get('top')
                fvg_bottom = fvg.get('bottom')
                overlap_top = min(ote_top, fvg_top)
                overlap_bottom = max(ote_bottom, fvg_bottom)
                if overlap_top > overlap_bottom:
                    overlap_mid = (overlap_top + overlap_bottom) / 2
                    score = 60
                    score += max(0, 10 - abs(overlap_mid - ote_mid) / tolerance_pips)
                    confluence_points.append({
                        'intersection_mid': overlap_mid,
                        'intersection_top': overlap_top,
                        'intersection_bottom': overlap_bottom,
                        'score': score,
                        'type': 'fvg',
                        'direction': direction,
                        'fvg': fvg,
                        'ote_zone': ote_zone
                    })
            return confluence_points
        except Exception as e:
            logger.error(f"[{symbol}] OTE/POI confluence analizi hatası: {e}", exc_info=True)
            return []

    def analyze(self, symbol: str, fib_levels: dict, order_blocks: list, fvgs: list):
        """
        OTE bölgeleri ile Order Block ve FVG gibi POI'ler arasındaki kesişimleri analiz eder.
        """
        analysis_result = {
            'confluences': [],
            'highest_rated_confluence': None
        }

        # OTE seviyeleri yoksa analiz yapma
        if 'ote_zones' not in fib_levels or not fib_levels['ote_zones']:
            return analysis_result

        all_confluences = []
        for direction, ote_zone in fib_levels['ote_zones'].items():
            if not ote_zone or 'entry_min' not in ote_zone:
                continue

            # Yeni helper metodu çağır
            confluence_points = self._find_ote_poi_confluence(
                ote_zone,
                order_blocks,
                fvgs,
                symbol
            )
            
            if confluence_points:
                for point in confluence_points:
                    point['direction'] = direction
                    all_confluences.append(point)

        analysis_result['confluences'] = all_confluences
        
        if all_confluences:
            # İsteğe bağlı: En yüksek puanlı kesişimi de bulabilirsiniz
            analysis_result['highest_rated_confluence'] = max(all_confluences, key=lambda x: x.get('score', 0))

        return analysis_result
    """
    OTE (Optimal Trade Entry) + Order Block Confluence Analizörü.
    
    Bu sınıf, Fibonacci OTE bölgeleri ile Order Block'ların kesişimini
    analiz ederek yüksek kaliteli giriş fırsatları tespit eder.
    """
    
    def __init__(self, 
                 ote_fib_min: float = 0.618,
                 ote_fib_max: float = 0.79,
                 min_confluence_score: float = 70.0,
                 proximity_tolerance_pct: float = 1.0):
        """
        OTE Confluence Analyzer'ı başlatır.
        
        Args:
            ote_fib_min: OTE bölgesinin alt sınırı (61.8%)
            ote_fib_max: OTE bölgesinin üst sınırı (79%)
            min_confluence_score: Minimum confluence skoru
            proximity_tolerance_pct: Yakınlık toleransı (%)
        """
        self.ote_fib_min = ote_fib_min
        self.ote_fib_max = ote_fib_max
        self.min_confluence_score = min_confluence_score
        self.proximity_tolerance_pct = proximity_tolerance_pct / 100
        
        logger.info(f"OTE Confluence Analyzer başlatıldı - "
                   f"OTE Zone: {ote_fib_min:.1%}-{ote_fib_max:.1%}, "
                   f"Min Score: {min_confluence_score}, "
                   f"Tolerance: {proximity_tolerance_pct}%")

    def analyze(self, symbol: str, 
                fibonacci_data: Dict[str, Any],
                order_block_data: Dict[str, Any],
                current_price: float) -> Dict[str, Any]:
        """
        Ana OTE + OB confluence analizi.
        
        Args:
            symbol: Sembol adı
            fibonacci_data: Fibonacci analiz sonuçları
            order_block_data: Order Block analiz sonuçları
            current_price: Güncel fiyat
            
        Returns:
            OTE Confluence analiz sonuçları
        """
        logger.info(f"[{symbol}] OTE Confluence analizi başlıyor...")
        
        try:
            # 1. Fibonacci verilerini doğrula
            if not self._validate_fibonacci_data(fibonacci_data):
                return self._empty_result("Geçersiz Fibonacci verisi")
            
            # 2. Order Block verilerini doğrula
            if not self._validate_order_block_data(order_block_data):
                return self._empty_result("Geçersiz Order Block verisi")
            
            # 3. OTE bölgelerini hesapla
            ote_zones = self._calculate_ote_zones(fibonacci_data)
            if not ote_zones:
                return self._empty_result("OTE bölgeleri hesaplanamadı")
            
            # 4. OTE + OB kesişimlerini bul
            confluences = self._find_ote_ob_intersections(
                ote_zones, order_block_data, current_price
            )
            
            # 5. Confluence'ları skorla ve filtrele
            scored_confluences = self._score_confluences(confluences, current_price)
            
            # 6. En iyi confluence'ları seç
            high_quality_confluences = [
                c for c in scored_confluences 
                if c['confluence_score'] >= self.min_confluence_score
            ]
            
            # 7. Sonuçları formatla
            result = {
                'confluences': high_quality_confluences,
                'ote_zones': ote_zones,
                'total_intersections': len(confluences),
                'high_quality_count': len(high_quality_confluences),
                'analysis_timestamp': datetime.now().isoformat(),
                'summary': self._generate_summary(high_quality_confluences)
            }
            
            logger.success(f"[{symbol}] OTE Confluence analizi tamamlandı: "
                          f"{len(confluences)} kesişim, "
                          f"{len(high_quality_confluences)} yüksek kalite")
            
            return result
            
        except Exception as e:
            logger.error(f"[{symbol}] OTE Confluence analizi hatası: {e}", exc_info=True)
            return self._empty_result(f"Analiz hatası: {str(e)}")

    def _validate_fibonacci_data(self, fibonacci_data: Dict[str, Any]) -> bool:
        """Fibonacci verilerini doğrular."""
        if not fibonacci_data or fibonacci_data.get('error', True):
            return False
            
        required_fields = ['range_high', 'range_low', 'direction']
        return all(field in fibonacci_data for field in required_fields)

    def _validate_order_block_data(self, order_block_data: Dict[str, Any]) -> bool:
        """Order Block verilerini doğrular."""
        if not order_block_data:
            return False
            
        bullish_obs = order_block_data.get('bullish_obs', [])
        bearish_obs = order_block_data.get('bearish_obs', [])
        
        return isinstance(bullish_obs, list) or isinstance(bearish_obs, list)

    def _calculate_ote_zones(self, fibonacci_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Fibonacci verilerinden OTE bölgelerini hesaplar.
        
        Args:
            fibonacci_data: Fibonacci analiz sonuçları
            
        Returns:
            OTE bölgeleri listesi
        """
        try:
            range_high = float(fibonacci_data['range_high'])
            range_low = float(fibonacci_data['range_low'])
            direction = fibonacci_data['direction']
            
            # Fibonacci retracement seviyeleri
            range_size = range_high - range_low
            
            if direction == 'bullish':
                # Bullish trend: High'dan Low'a retracement
                ote_top = range_high - (range_size * self.ote_fib_min)
                ote_bottom = range_high - (range_size * self.ote_fib_max)
                
                ote_zone = {
                    'type': 'BULLISH_OTE',
                    'direction': 'bullish',
                    'top': ote_top,
                    'bottom': ote_bottom,
                    'mid': (ote_top + ote_bottom) / 2,
                    'fib_range': f"{self.ote_fib_min:.1%}-{self.ote_fib_max:.1%}",
                    'impulse_high': range_high,
                    'impulse_low': range_low
                }
                
            else:  # bearish
                # Bearish trend: Low'dan High'a retracement  
                ote_bottom = range_low + (range_size * self.ote_fib_min)
                ote_top = range_low + (range_size * self.ote_fib_max)
                
                ote_zone = {
                    'type': 'BEARISH_OTE',
                    'direction': 'bearish',
                    'top': ote_top,
                    'bottom': ote_bottom,
                    'mid': (ote_top + ote_bottom) / 2,
                    'fib_range': f"{self.ote_fib_min:.1%}-{self.ote_fib_max:.1%}",
                    'impulse_high': range_high,
                    'impulse_low': range_low
                }
            
            logger.debug(f"OTE Zone hesaplandı: {ote_zone['type']} | "
                        f"Top: {ote_zone['top']:.6f} | "
                        f"Bottom: {ote_zone['bottom']:.6f}")
            
            return [ote_zone]
            
        except Exception as e:
            logger.error(f"OTE bölgesi hesaplama hatası: {e}")
            return []

    def _find_ote_ob_intersections(self, 
                                  ote_zones: List[Dict[str, Any]],
                                  order_block_data: Dict[str, Any],
                                  current_price: float) -> List[Dict[str, Any]]:
        """
        OTE bölgeleri ile Order Block'ların kesişimlerini bulur.
        
        Args:
            ote_zones: OTE bölgeleri
            order_block_data: Order Block verileri
            current_price: Güncel fiyat
            
        Returns:
            Kesişim listesi
        """
        intersections = []
        
        # Tüm order block'ları al
        all_obs = []
        
        bullish_obs = order_block_data.get('bullish_obs', [])
        bearish_obs = order_block_data.get('bearish_obs', [])
        
        if isinstance(bullish_obs, list):
            all_obs.extend(bullish_obs)
        if isinstance(bearish_obs, list):
            all_obs.extend(bearish_obs)
        
        logger.debug(f"Kesişim analizi: {len(ote_zones)} OTE zone, {len(all_obs)} OB")
        
        for ote_zone in ote_zones:
            for order_block in all_obs:
                intersection = self._check_ote_ob_intersection(ote_zone, order_block, current_price)
                if intersection:
                    intersections.append(intersection)
        
        return intersections

    def _check_ote_ob_intersection(self, 
                                  ote_zone: Dict[str, Any],
                                  order_block: Dict[str, Any],
                                  current_price: float) -> Optional[Dict[str, Any]]:
        """
        Tek bir OTE bölgesi ile Order Block arasındaki kesişimi kontrol eder.
        
        Args:
            ote_zone: OTE bölgesi
            order_block: Order Block
            current_price: Güncel fiyat
            
        Returns:
            Kesişim varsa interseksiyon bilgileri, yoksa None
        """
        try:
            # OTE zone bilgileri
            ote_top = ote_zone['top']
            ote_bottom = ote_zone['bottom']
            ote_direction = ote_zone['direction']
            
            # Order Block bilgileri
            ob_high = float(order_block.get('high', 0))
            ob_low = float(order_block.get('low', 0))
            ob_type = order_block.get('type', '').lower()
            
            # Kesişim kontrolü
            overlap_top = min(ote_top, ob_high)
            overlap_bottom = max(ote_bottom, ob_low)
            
            # Kesişim var mı?
            if overlap_top <= overlap_bottom:
                return None
            
            # Kesişim alanı hesapla
            overlap_size = overlap_top - overlap_bottom
            ote_size = ote_top - ote_bottom
            ob_size = ob_high - ob_low
            
            overlap_pct_ote = (overlap_size / ote_size) * 100
            overlap_pct_ob = (overlap_size / ob_size) * 100
            
            # Direction uyumu kontrol et
            direction_match = (
                (ote_direction == 'bullish' and 'bull' in ob_type) or
                (ote_direction == 'bearish' and 'bear' in ob_type)
            )
            
            # Kesişim bilgilerini oluştur
            intersection = {
                'ote_zone': ote_zone,
                'order_block': order_block,
                'intersection_top': overlap_top,
                'intersection_bottom': overlap_bottom,
                'intersection_mid': (overlap_top + overlap_bottom) / 2,
                'intersection_size': overlap_size,
                'overlap_pct_ote': overlap_pct_ote,
                'overlap_pct_ob': overlap_pct_ob,
                'direction_match': direction_match,
                'direction': ote_direction,
                'distance_from_price': abs(((overlap_top + overlap_bottom) / 2) - current_price),
                'distance_from_price_pct': abs(((((overlap_top + overlap_bottom) / 2) - current_price) / current_price) * 100)
            }
            
            logger.debug(f"Kesişim bulundu: {ote_direction.upper()} OTE + {ob_type.upper()} OB | "
                        f"Overlap: {overlap_pct_ote:.1f}% OTE, {overlap_pct_ob:.1f}% OB | "
                        f"Direction Match: {direction_match}")
            
            return intersection
            
        except Exception as e:
            logger.error(f"Kesişim kontrolü hatası: {e}")
            return None

    def _score_confluences(self, 
                          confluences: List[Dict[str, Any]],
                          current_price: float) -> List[Dict[str, Any]]:
        """
        Confluence'ları kalite skoruna göre puanlar.
        
        Args:
            confluences: Confluence listesi
            current_price: Güncel fiyat
            
        Returns:
            Skorlanmış confluence listesi
        """
        scored_confluences = []
        
        for confluence in confluences:
            score = self._calculate_confluence_score(confluence, current_price)
            confluence['confluence_score'] = score
            confluence['quality_rating'] = self._get_quality_rating(score)
            scored_confluences.append(confluence)
        
        # Skora göre sırala (yüksekten düşüğe)
        scored_confluences.sort(key=lambda x: x['confluence_score'], reverse=True)
        
        return scored_confluences

    def _calculate_confluence_score(self, 
                                   confluence: Dict[str, Any],
                                   current_price: float) -> float:
        """
        Tek bir confluence'ın kalite skorunu hesaplar (0-100).
        
        Args:
            confluence: Confluence verisi
            current_price: Güncel fiyat
            
        Returns:
            Confluence skoru (0-100)
        """
        score = 0.0
        
        try:
            # 1. Overlap kalitesi (40 puan)
            overlap_ote = confluence['overlap_pct_ote']
            overlap_ob = confluence['overlap_pct_ob']
            
            # İdeal overlap: %50+ için her iki alan
            overlap_score = min(40, (min(overlap_ote, overlap_ob) / 50) * 40)
            score += overlap_score
            
            # 2. Direction uyumu (25 puan)
            if confluence['direction_match']:
                score += 25
            
            # 3. Fiyat yakınlığı (20 puan)
            distance_pct = confluence['distance_from_price_pct']
            if distance_pct <= 1.0:  # %1 içinde
                proximity_score = 20
            elif distance_pct <= 3.0:  # %3 içinde
                proximity_score = 15
            elif distance_pct <= 5.0:  # %5 içinde
                proximity_score = 10
            else:
                proximity_score = max(0, 20 - (distance_pct * 2))
            
            score += proximity_score
            
            # 4. Order Block kalitesi (15 puan)
            order_block = confluence['order_block']
            ob_strength = order_block.get('strength', 'medium').lower()
            
            if ob_strength == 'strong':
                score += 15
            elif ob_strength == 'medium':
                score += 10
            else:  # weak
                score += 5
            
            # Bonus puanlar
            # 5. Büyük overlap (bonus +5)
            if min(overlap_ote, overlap_ob) >= 80:
                score += 5
            
            # 6. Çok yakın fiyat (bonus +5)
            if distance_pct <= 0.5:
                score += 5
            
            # Skor sınırlaması
            score = min(100, max(0, score))
            
            logger.debug(f"Confluence score hesaplandı: {score:.1f} | "
                        f"Overlap: {overlap_score:.1f}, Direction: {25 if confluence['direction_match'] else 0}, "
                        f"Proximity: {proximity_score:.1f}, OB Quality: {15 if ob_strength == 'strong' else 10 if ob_strength == 'medium' else 5}")
            
            return score
            
        except Exception as e:
            logger.error(f"Confluence score hesaplama hatası: {e}")
            return 0.0

    def _get_quality_rating(self, score: float) -> str:
        """Skora göre kalite derecelendirmesi."""
        if score >= 85:
            return "EXCELLENT"
        elif score >= 75:
            return "VERY_GOOD"
        elif score >= 65:
            return "GOOD"
        elif score >= 50:
            return "FAIR"
        else:
            return "POOR"

    def _generate_summary(self, confluences: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Confluence analizi özeti oluşturur."""
        if not confluences:
            return {
                'best_confluence': None,
                'total_count': 0,
                'avg_score': 0.0,
                'quality_distribution': {}
            }
        
        scores = [c['confluence_score'] for c in confluences]
        best_confluence = confluences[0] if confluences else None
        
        # Kalite dağılımı
        quality_dist = {}
        for confluence in confluences:
            quality = confluence['quality_rating']
            quality_dist[quality] = quality_dist.get(quality, 0) + 1
        
        return {
            'best_confluence': best_confluence,
            'total_count': len(confluences),
            'avg_score': sum(scores) / len(scores),
            'max_score': max(scores),
            'min_score': min(scores),
            'quality_distribution': quality_dist
        }

    def _empty_result(self, reason: str = "Analiz yapılamadı") -> Dict[str, Any]:
        """Boş analiz sonucu döndürür."""
        return {
            'confluences': [],
            'ote_zones': [],
            'total_intersections': 0,
            'high_quality_count': 0,
            'analysis_timestamp': datetime.now().isoformat(),
            'error': True,
            'error_reason': reason,
            'summary': {
                'best_confluence': None,
                'total_count': 0,
                'avg_score': 0.0,
                'quality_distribution': {}
            }
        }

    def get_best_confluence_signal(self, 
                                  analysis_result: Dict[str, Any],
                                  symbol: str) -> Optional[Dict[str, Any]]:
        """
        En iyi confluence'dan ticaret sinyali oluşturur.
        
        Args:
            analysis_result: OTE Confluence analiz sonucu
            symbol: Sembol adı
            
        Returns:
            Ticaret sinyali veya None
        """
        confluences = analysis_result.get('confluences', [])
        if not confluences:
            return None
        
        best_confluence = confluences[0]  # En yüksek skorlu
        
        # Sinyal oluştur
        signal = {
            'type': 'OTE_OB_CONFLUENCE',
            'symbol': symbol,
            'direction': 'bull' if best_confluence['direction'] == 'bullish' else 'bear',
            'price': best_confluence['intersection_mid'],
            'confidence': best_confluence['confluence_score'] / 100,
            'reason': f"OTE Zone + Order Block Confluence (Score: {best_confluence['confluence_score']:.1f})",
            'confluence_data': best_confluence,
            'entry_zone_top': best_confluence['intersection_top'],
            'entry_zone_bottom': best_confluence['intersection_bottom'],
            'quality_rating': best_confluence['quality_rating'],
            'ote_zone_info': best_confluence['ote_zone'],
            'order_block_info': best_confluence['order_block']
        }
        
        logger.success(f"[{symbol}] En iyi OTE Confluence sinyali oluşturuldu: "
                      f"{signal['direction'].upper()} @ {signal['price']:.6f} "
                      f"(Score: {best_confluence['confluence_score']:.1f})")
        
        return signal
